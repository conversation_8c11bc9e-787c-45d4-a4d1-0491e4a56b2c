%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Deedy - One Page Two Column Resume
% LaTeX Template
% Version 1.1 (30/4/2014)
%
% Original author:
% Debarghya Das (http://debarghyadas.com)
%
% Original repository:
% https://github.com/deedydas/Deedy-Resume
%
% IMPORTANT: THIS TEMPLATE NEEDS TO BE COMPILED WITH XeLaTeX
%
% This template uses several fonts not included with Windows/Linux by
% default. If you get compilation errors saying a font is missing, find the line
% on which the font is used and either change it to a font included with your
% operating system or comment the line out to use the default font.
% 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% 
% TODO:
% 1. Integrate biber/bibtex for article citation under publications.
% 2. Figure out a smoother way for the document to flow onto the next page.
% 3. Add styling information for a "Projects/Hacks" section.
% 4. Add location/address information
% 5. Merge OpenFont and MacFonts as a single sty with options.
% 
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% CHANGELOG:
% v1.1:
% 1. Fixed several compilation bugs with \renewcommand
% 2. Got Open-source fonts (Windows/Linux support)
% 3. Added Last Updated
% 4. Move Title styling into .sty
% 5. Commented .sty file.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
% Known Issues:
% 1. Overflows onto second page if any column's contents are more than the
% vertical limit
% 2. Hacky space on the first bullet point on the second column.
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\documentclass[]{deedy-resume-openfont}

\begin{document}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
%     TITLE NAME
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\namesection{廖博文}{}{ \urlstyle{same}\url{https://github.com/xiadengma} \\
\href{mailto:<EMAIL>}{<EMAIL>} | +86-139-6701-4517
}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
%     COLUMN ONE
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\begin{minipage}[t]{0.33\textwidth} 

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%     EDUCATION
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%%% 利用tikz来定位照片，部分招聘单位可能需要“以貌取人”
\begin{tikzpicture}[remember picture, overlay] 
  \node[anchor = north west] at ($(current page.north west)+(+1cm,-4cm)$) {\includegraphics[height=4.2cm]{avatar}};
\end{tikzpicture}%

~\\
~\\
~\\
~\\
~\\
~\\
~\\
~\\
~\\
~\\

\section{教育背景} 

\subsection{华北科技学院}
\descript{本科专业：电子信息工程}
\sectionsep

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%     LINKS
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{链接} 
GitHub://  \href{https://github.com/xiadengma}{\custombold{@xiadengma}} \\
个人网站://  \href{https://xiadengma.com}{\custombold{xiadengma.com}} \\
Blog:// \href{https://blog.xiadengma.com}{\custombold{xiadengmaのblog}} \\
\sectionsep

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%     COURSEWORK
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{自我介绍}
\subsection{应聘 \quad 算法实习生}
\quad \\
熟悉C/C++语言 \\
理解Linux/Unix系统并熟练使用 \\
热衷于学习新技术 \\
对计算机视觉充满极大热情 \\
希望成为一名优秀的算法工程师 \\
\sectionsep

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%     SKILLS
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{专业技能}
\textbullet{}   C/C++ \textbullet{}  Python  \textbullet{} Linux \\
\textbullet{} OpenCV \textbullet{} QT \textbullet{} ML \\ 
\textbullet{} DL \textbullet{} Data analysis  \textbullet{} Git\\ 
\sectionsep

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
%     COLUMN TWO
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\end{minipage} 
\hfill
\begin{minipage}[t]{0.66\textwidth} 

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%     EXPERIENCE
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{在校经历}

\runsubsection{全国大学生机器人大赛RoboMaster机甲大师赛}
\descript{| 创新实验室算法组 }
\location{2020年9月 – 2021年10月 | 中国，河北}
\vspace{\topsep} % Hacky fix for awkward extra vertical space
\begin{tightemize}
\item 作为算法组负责人和操作手，同另外四位队员进行机器人视觉识别工作及对应的算法。
\item 获得了RoboMaster高校联盟赛-3v3对抗赛一等奖及RoboMaster高校联盟赛-步兵对抗一等奖
\item 获得了RoboMaster超级对抗赛区域赛（北部赛区）一等奖
\item 获得了RoboMaster超级对抗赛全国赛二等奖
\end{tightemize}
\sectionsep

\runsubsection{MathorCup高校数学建模挑战赛}
\descript{| 钢板切割优化 }
\location{2020年四月 | 中国，河北}
\begin{tightemize}
\item 同另外两位队友一起参加了数学建模挑战赛。
\end{tightemize}
\sectionsep

\runsubsection{河北省大学生科学技能竞赛“华为杯”}
\descript{| 机械臂智能识别分拣 }
\location{2020年12月 | 中国河北}
\begin{tightemize}
\item 作为识别算法负责人，同另外九位队员一起合作完成了最终的机械臂智能识别分拣任务。
\item 获得了河北省大学生科学技能竞赛一等奖
\end{tightemize}
\sectionsep

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%     RESEARCH
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{个人项目}

\runsubsection{\href{}{{OpenCV实现图片集蒙太奇马赛克}}}
\descript{}
\location{2021年12月 | 华北科技学院}
读取图片集文件夹内所有图片、依次识别主色调并重命名所有图片。再依次匹配图片矩阵和图片集，最终实现蒙太奇马赛克。
\sectionsep

\runsubsection{\href{}{{OpenCV实现ASCII艺术}}}
\descript{}
\location{2021年12月 | 华北科技学院}
读取图片或者视频,计算图片矩阵的相对灰度并在终端中输出，最终实现ASCII艺术。
\sectionsep

\runsubsection{\href{}{{RoboMaster机甲大师步兵及英雄视觉识别算法}}}
\descript{}
\location{2021年5月-2021年7月 | 华北科技学院}
使用大华工业相机实现实时识别机器人装甲板，并同时识别相对距离，根据弹丸种类以及初始速度解算出当前云台的Yaw、Pitch并通过串口通信发送给STM32控制板。
同时步兵实现正弦波旋转能量机关的识别及击打。
\sectionsep

\runsubsection{\href{}{{华为杯华北科技学院B组识别算法}}}
\descript{}
\location{2020年12月 | 华北科技学院}
通过普通USB相机实现实时识别卡片颜色、角度以及具体的方向，并通过串口通信发送给STM32控制板。
\sectionsep

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%     SOCIETIES
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{interests} 

\textbullet{} Computer Vision\\
\textbullet{} Tech savvying\\
\textbullet{} Reading\\
\sectionsep

\end{minipage} 
\end{document}
