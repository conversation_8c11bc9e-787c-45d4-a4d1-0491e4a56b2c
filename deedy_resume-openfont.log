This is XeTeX, Version 3.141592653-2.6-0.999994 (TeX Live 2022) (preloaded format=xelatex 2022.4.14)  9 FEB 2023 21:01
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**deedy_resume-openfont
(./deedy_resume-openfont.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-02-24> (./deedy-resume-openfont.cls
Document Class: deedy-resume-openfont 2014/04/30 CV class
(/usr/local/texlive/2022/texmf-dist/tex/latex/base/article.cls
Document Class: article 2021/10/04 v1.4n Standard LaTeX document class
(/usr/local/texlive/2022/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2021/10/04 v1.4n Standard LaTeX file (size option)
)
\c@part=\count181
\c@section=\count182
\c@subsection=\count183
\c@subsubsection=\count184
\c@paragraph=\count185
\c@subparagraph=\count186
\c@figure=\count187
\c@table=\count188
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
) (/usr/local/texlive/2022/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2022/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks16
) (/usr/local/texlive/2022/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/local/texlive/2022/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count189
\Gm@cntv=\count190
\c@Gm@tempcnt=\count191
\Gm@bindingoffset=\dimen139
\Gm@wd@mp=\dimen140
\Gm@odd@mp=\dimen141
\Gm@even@mp=\dimen142
\Gm@layoutwidth=\dimen143
\Gm@layoutheight=\dimen144
\Gm@layouthoffset=\dimen145
\Gm@layoutvoffset=\dimen146
\Gm@dimlist=\toks17
) (/usr/local/texlive/2022/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2021/10/31 v2.13 LaTeX color extensions (UK)
 (/usr/local/texlive/2022/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Package option `usenames' ignored on input line 218.
Package xcolor Info: Driver file: xetex.def on input line 227.
 (/usr/local/texlive/2022/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2021/03/18 v5.0k Graphics/color driver for xetex
)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1352.
Package xcolor Info: Model `RGB' extended on input line 1368.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1370.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1372.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1373.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1374.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1375.
 (/usr/local/texlive/2022/texmf-dist/tex/latex/graphics/dvipsnam.def
File: dvipsnam.def 2016/06/17 v3.0m Driver-dependent file (DPC,SPQR)
)) (/usr/local/texlive/2022/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2022-02-21 v7.00n Hypertext links for LaTeX
 (/usr/local/texlive/2022/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/local/texlive/2022/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
) (/usr/local/texlive/2022/texmf-dist/tex/generic/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/letltxmacro/letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)
)
\@linkdim=\dimen147
\Hy@linkcounter=\count192
\Hy@pagecounter=\count193
 (/usr/local/texlive/2022/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2022-02-21 v7.00n Hyperref: PDFDocEncoding definition (HO)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count194
 (/usr/local/texlive/2022/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2022-02-21 v7.00n Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Hyper figures OFF on input line 4137.
Package hyperref Info: Link nesting OFF on input line 4142.
Package hyperref Info: Hyper index ON on input line 4145.
Package hyperref Info: Plain pages OFF on input line 4152.
Package hyperref Info: Backreferencing OFF on input line 4157.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4390.
\c@Hy@tempcnt=\count195
 (/usr/local/texlive/2022/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4749.
\XeTeXLinkMargin=\dimen148
 (/usr/local/texlive/2022/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/local/texlive/2022/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count196
\Field@Width=\dimen149
\Fld@charsize=\dimen150
Package hyperref Info: Hyper figures OFF on input line 6027.
Package hyperref Info: Link nesting OFF on input line 6032.
Package hyperref Info: Hyper index ON on input line 6035.
Package hyperref Info: backreferencing OFF on input line 6042.
Package hyperref Info: Link coloring OFF on input line 6047.
Package hyperref Info: Link coloring with OCG OFF on input line 6052.
Package hyperref Info: PDF/A mode OFF on input line 6057.
LaTeX Info: Redefining \ref on input line 6097.
LaTeX Info: Redefining \pageref on input line 6101.
 (/usr/local/texlive/2022/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count197
\c@Item=\count198
\c@Hfootnote=\count199
)
Package hyperref Info: Driver (autodetected): hxetex.
 (/usr/local/texlive/2022/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2022-02-21 v7.00n Hyperref driver for XeTeX
 (/usr/local/texlive/2022/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box50
\c@Hy@AnnotLevel=\count266
\HyField@AnnotCount=\count267
\Fld@listcount=\count268
\c@bookmark@seq@number=\count269
 (/usr/local/texlive/2022/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)
 (/usr/local/texlive/2022/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (/usr/local/texlive/2022/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 286.
)
\Hy@SectionHShift=\skip49
) (/usr/local/texlive/2022/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2021/07/05 v2.14 Sectioning titles
\ttl@box=\box51
\beforetitleunit=\skip50
\aftertitleunit=\skip51
\ttl@plus=\dimen151
\ttl@minus=\dimen152
\ttl@toksa=\toks18
\titlewidth=\dimen153
\titlewidthlast=\dimen154
\titlewidthfirst=\dimen155
) (/usr/local/texlive/2022/texmf-dist/tex/latex/textpos/textpos.sty
Package: textpos 2020/09/26 v1.10

Package: textpos 2020/09/26 1.10, absolute positioning of text on the page
Package textpos Info: choosing support for LaTeX3 on input line 61.
\TP@textbox=\box52
\TP@holdbox=\box53
\TPHorizModule=\dimen156
\TPVertModule=\dimen157
\TP@margin=\dimen158
\TP@absmargin=\dimen159
Grid set 16 x 16 = 38.39343pt x 49.68562pt
\TPboxrulesize=\dimen160
\TP@ox=\dimen161
\TP@oy=\dimen162
\TP@tbargs=\toks19
TextBlockOrigin set to 0pt x 0pt
) (/usr/local/texlive/2022/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2022/02/26 3.73 The Babel package
\babel@savecnt=\count270
\U@D=\dimen163
\l@unhyphenated=\language87
 (/usr/local/texlive/2022/texmf-dist/tex/generic/babel/xebabel.def (/usr/local/texlive/2022/texmf-dist/tex/generic/babel/txtbabel.def))
\bbl@readstream=\read2
\bbl@dirlevel=\count271
 (/usr/local/texlive/2022/texmf-dist/tex/generic/babel-english/UKenglish.ldf
Language: UKenglish 2017/06/06 v3.3r English support from the babel system
 (/usr/local/texlive/2022/texmf-dist/tex/generic/babel-english/english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language21). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language21). Reported on input line 108.
))) (/usr/local/texlive/2022/texmf-dist/tex/latex/isodate/isodate.sty
Package: isodate 2010/01/03 v2.30 Print dates with different formats (HH)
 (/usr/local/texlive/2022/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2020/11/24 v1.1c Standard LaTeX ifthen package (DPC)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/substr/substr.sty
Package: substr 2009/10/20 v1.2 Handle substrings
\c@su@anzahl=\count272
) (/usr/local/texlive/2022/texmf-dist/tex/latex/isodate/english.idf
File: english.idf 2010/01/03 v2.30 Language definitions for isodate package (HH)

Define commands for English date format
)
\c@iso@tmpmonth=\count273
\c@iso@yeartwo=\count274
\c@iso@slash=\count275
\c@iso@minus=\count276
\c@iso@dot=\count277
\c@iso@@slash=\count278
\c@iso@@minus=\count279
\c@iso@@dot=\count280
) (/usr/local/texlive/2022/texmf-dist/tex/latex/fontspec/fontspec.sty (/usr/local/texlive/2022/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (/usr/local/texlive/2022/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2022-02-24 L3 programming layer (loader) 
 (/usr/local/texlive/2022/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2022-02-07 L3 backend support: XeTeX
 (|extractbb --version)
\c__kernel_sys_dvipdfmx_version_int=\count281
\l__color_backend_stack_int=\count282
\g__color_backend_stack_int=\count283
\g__graphics_track_int=\count284
\l__pdf_internal_box=\box54
\g__pdf_backend_object_int=\count285
\g__pdf_backend_annotation_int=\count286
\g__pdf_backend_link_int=\count287
))
Package: xparse 2022-01-12 L3 Experimental document command parser
)
Package: fontspec 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
 (/usr/local/texlive/2022/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2022/01/15 v2.8a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count288
\l__fontspec_language_int=\count289
\l__fontspec_strnum_int=\count290
\l__fontspec_tmp_int=\count291
\l__fontspec_tmpa_int=\count292
\l__fontspec_tmpb_int=\count293
\l__fontspec_tmpc_int=\count294
\l__fontspec_em_int=\count295
\l__fontspec_emdef_int=\count296
\l__fontspec_strong_int=\count297
\l__fontspec_strongdef_int=\count298
\l__fontspec_tmpa_dim=\dimen164
\l__fontspec_tmpb_dim=\dimen165
\l__fontspec_tmpc_dim=\dimen166
 (/usr/local/texlive/2022/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/usr/local/texlive/2022/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (/usr/local/texlive/2022/texmf-dist/tex/xelatex/xltxtra/xltxtra.sty
Package: xltxtra 2018/12/31 v0.7 Improvements for the "XeLaTeX" format
 (/usr/local/texlive/2022/texmf-dist/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
) (/usr/local/texlive/2022/texmf-dist/tex/generic/iftex/ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
) (/usr/local/texlive/2022/texmf-dist/tex/latex/realscripts/realscripts.sty
Package: realscripts 2016/02/13 v0.3d Access OpenType subscripts and superscripts
\subsupersep=\dimen167
) (/usr/local/texlive/2022/texmf-dist/tex/latex/metalogo/metalogo.sty
Package: metalogo 2010/05/29 v0.12 Extended TeX logo macros
 (/usr/local/texlive/2022/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2022/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2021/03/04 v1.4d Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2022/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
)
\Gin@req@height=\dimen168
\Gin@req@width=\dimen169
)
\xl@everylogo=\toks20
\xl@@everylogo=\toks21
LaTeX Info: Redefining \TeX on input line 193.
LaTeX Info: Redefining \LaTeX on input line 202.
LaTeX Info: Redefining \LaTeXe on input line 219.
)
\l__xetex_show_hyphens_wrapping_box=\box55
\l__xetex_show_hyphens_temp_box=\box56
\l__xetex_show_hyphens_final_box=\box57
\g__xetex_show_hyphens_word_box=\box58
) (/usr/local/texlive/2022/texmf-dist/tex/xelatex/xunicode/xunicode.sty
File: xunicode.sty 2011/09/09 v0.981 provides access to latin accents and many other characters in Unicode lower plane
 (/usr/local/texlive/2022/texmf-dist/tex/latex/tipa/t3enc.def
File: t3enc.def 2001/12/31 T3 encoding
)
\tipaTiiicode=\count299
\tipasavetokens=\toks22
\tipachecktokens=\toks23
) (/usr/local/texlive/2022/texmf-dist/tex/latex/ctex/ctex.sty
Package: ctex 2021/12/12 v2.5.8 Chinese adapter in LaTeX (CTEX)
 (/usr/local/texlive/2022/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2022-01-12 LaTeX2e option processing using LaTeX3 keys
) (/usr/local/texlive/2022/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2021/12/12 v2.5.8 Document and package hooks (CTEX)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2021/12/12 v2.5.8 Patching commands (CTEX)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (/usr/local/texlive/2022/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count300
\l__ctex_tmp_box=\box59
\l__ctex_tmp_dim=\dimen170
\g__ctex_section_depth_int=\count301
\g__ctex_font_size_int=\count302
 (/usr/local/texlive/2022/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2021/12/12 v2.5.8 Option configuration file (CTEX)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2021/12/12 v2.5.8 XeLaTeX adapter (CTEX)
 (/usr/local/texlive/2022/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2021/12/12 v3.8.8 Typesetting CJK scripts with XeLaTeX
 (/usr/local/texlive/2022/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.sty
Package: xtemplate 2022-01-12 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen171
\l__xtemplate_tmp_int=\count303
\l__xtemplate_tmp_muskip=\muskip17
\l__xtemplate_tmp_skip=\skip52
)
\l__xeCJK_tmp_int=\count304
\l__xeCJK_tmp_box=\box60
\l__xeCJK_tmp_dim=\dimen172
\l__xeCJK_tmp_skip=\skip53
\g__xeCJK_space_factor_int=\count305
\l__xeCJK_begin_int=\count306
\l__xeCJK_end_int=\count307
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip54
\g__xeCJK_node_int=\count308
\c__xeCJK_CJK_node_dim=\dimen173
\c__xeCJK_CJK-space_node_dim=\dimen174
\c__xeCJK_default_node_dim=\dimen175
\c__xeCJK_default-space_node_dim=\dimen176
\c__xeCJK_CJK-widow_node_dim=\dimen177
\c__xeCJK_normalspace_node_dim=\dimen178
\l__xeCJK_ccglue_skip=\skip55
\l__xeCJK_ecglue_skip=\skip56
\l__xeCJK_punct_kern_skip=\skip57
\l__xeCJK_last_penalty_int=\count309
\l__xeCJK_last_bound_dim=\dimen179
\l__xeCJK_last_kern_dim=\dimen180
\l__xeCJK_widow_penalty_int=\count310

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2337.

\l__xeCJK_fixed_punct_width_dim=\dimen181
\l__xeCJK_mixed_punct_width_dim=\dimen182
\l__xeCJK_middle_punct_width_dim=\dimen183
\l__xeCJK_fixed_margin_width_dim=\dimen184
\l__xeCJK_mixed_margin_width_dim=\dimen185
\l__xeCJK_middle_margin_width_dim=\dimen186
\l__xeCJK_bound_punct_width_dim=\dimen187
\l__xeCJK_bound_margin_width_dim=\dimen188
\l__xeCJK_margin_minimum_dim=\dimen189
\l__xeCJK_kerning_total_width_dim=\dimen190
\l__xeCJK_same_align_margin_dim=\dimen191
\l__xeCJK_different_align_margin_dim=\dimen192
\l__xeCJK_kerning_margin_width_dim=\dimen193
\l__xeCJK_kerning_margin_minimum_dim=\dimen194
\l__xeCJK_bound_dim=\dimen195
\l__xeCJK_reverse_bound_dim=\dimen196
\l__xeCJK_margin_dim=\dimen197
\l__xeCJK_minimum_bound_dim=\dimen198
\l__xeCJK_kerning_margin_dim=\dimen199
\g__xeCJK_family_int=\count311
\l__xeCJK_fam_int=\count312
\g__xeCJK_fam_allocation_int=\count313
\l__xeCJK_verb_case_int=\count314
\l__xeCJK_verb_exspace_skip=\skip58
 (/usr/local/texlive/2022/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2021/12/12 v3.8.8 Configuration file for xeCJK package
))
\ccwd=\dimen256
\l__ctex_ccglue_skip=\skip59
)
\l__ctex_ziju_dim=\dimen257
 (/usr/local/texlive/2022/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2020/05/01 v2.8 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count315
 (/usr/local/texlive/2022/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2020/05/01 v2.8 Chinese numerals with UTF8 encoding
)) (/usr/local/texlive/2022/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-plain.def
File: ctex-scheme-plain.def 2021/12/12 v2.5.8 Plain scheme for generic (CTEX)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-fandol.def
File: ctex-fontset-fandol.def 2021/12/12 v2.5.8 Fandol fonts definition (CTEX)


Package fontspec Warning: Font "FandolSong-Regular" does not contain requested
(fontspec)                Script "CJK".


Package fontspec Info: Font family 'FandolSong-Regular(0)' created for font
(fontspec)             'FandolSong-Regular' with options
(fontspec)             [Script={CJK},Extension={.otf},BoldFont={FandolSong-Bold},ItalicFont={FandolKai-Regular}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Bold.otf]/OT:language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"[FandolKai-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

)) (/usr/local/texlive/2022/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2021/12/12 v2.5.8 Configuration file (CTEX)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/local/texlive/2022/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/usr/local/texlive/2022/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks24
\pgfutil@tempdima=\dimen258
\pgfutil@tempdimb=\dimen259
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.tex)) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box61
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2021/05/15 v3.1.9a (3.1.9a)
))
Package: pgf 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/local/texlive/2022/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/local/texlive/2022/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks25
\pgfkeys@temptoks=\toks26
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.tex
\pgfkeys@tmptoks=\toks27
))
\pgf@x=\dimen260
\pgf@y=\dimen261
\pgf@xa=\dimen262
\pgf@ya=\dimen263
\pgf@xb=\dimen264
\pgf@yb=\dimen265
\pgf@xc=\dimen266
\pgf@yc=\dimen267
\pgf@xd=\dimen268
\pgf@yd=\dimen269
\w@pgf@writea=\write3
\r@pgf@reada=\read3
\c@pgf@counta=\count316
\c@pgf@countb=\count317
\c@pgf@countc=\count318
\c@pgf@countd=\count319
\t@pgf@toka=\toks28
\t@pgf@tokb=\toks29
\t@pgf@tokc=\toks30
\pgf@sys@id@count=\count320
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2021/05/15 v3.1.9a (3.1.9a)
)
Driver file for pgf: pgfsys-xetex.def
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2021/05/15 v3.1.9a (3.1.9a)
)
\pgfsys@objnum=\count321
))) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfsyssoftpath@smallbuffer@items=\count322
\pgfsyssoftpath@bigbuffer@items=\count323
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen270
\pgfmath@count=\count324
\pgfmath@box=\box62
\pgfmath@toks=\toks31
\pgfmath@stack@operand=\toks32
\pgfmath@stack@operation=\toks33
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex))) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count325
)) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@picminx=\dimen271
\pgf@picmaxx=\dimen272
\pgf@picminy=\dimen273
\pgf@picmaxy=\dimen274
\pgf@pathminx=\dimen275
\pgf@pathmaxx=\dimen276
\pgf@pathminy=\dimen277
\pgf@pathmaxy=\dimen278
\pgf@xx=\dimen279
\pgf@xy=\dimen280
\pgf@yx=\dimen281
\pgf@yy=\dimen282
\pgf@zx=\dimen283
\pgf@zy=\dimen284
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@path@lastx=\dimen285
\pgf@path@lasty=\dimen286
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@shorten@end@additional=\dimen287
\pgf@shorten@start@additional=\dimen288
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfpic=\box63
\pgf@hbox=\box64
\pgf@layerbox@main=\box65
\pgf@picture@serial@count=\count326
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgflinewidth=\dimen289
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@pt@x=\dimen290
\pgf@pt@y=\dimen291
\pgf@pt@temp=\dimen292
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfarrowsep=\dimen293
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@max=\dimen294
\pgf@sys@shading@range@num=\count327
\pgf@shadingcount=\count328
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfexternal@startupbox=\box66
)) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfnodeparttextbox=\box67
) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2021/05/15 v3.1.9a (3.1.9a)
) (/usr/local/texlive/2022/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2021/05/15 v3.1.9a (3.1.9a)
\pgf@nodesepstart=\dimen295
\pgf@nodesepend=\dimen296
) (/usr/local/texlive/2022/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2021/05/15 v3.1.9a (3.1.9a)
)) (/usr/local/texlive/2022/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/local/texlive/2022/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/usr/local/texlive/2022/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen297
\pgffor@skip=\dimen298
\pgffor@stack=\toks34
\pgffor@toks=\toks35
)) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2021/05/15 v3.1.9a (3.1.9a)
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@plot@mark@count=\count329
\pgfplotmarksize=\dimen299
)
\tikz@lastx=\dimen300
\tikz@lasty=\dimen301
\tikz@lastxsaved=\dimen302
\tikz@lastysaved=\dimen303
\tikz@lastmovetox=\dimen304
\tikz@lastmovetoy=\dimen305
\tikzleveldistance=\dimen306
\tikzsiblingdistance=\dimen307
\tikz@figbox=\box68
\tikz@figbox@bg=\box69
\tikz@tempbox=\box70
\tikz@tempbox@bg=\box71
\tikztreelevel=\count330
\tikznumberofchildren=\count331
\tikznumberofcurrentchild=\count332
\tikz@fig@count=\count333
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfmatrixcurrentrow=\count334
\pgfmatrixcurrentcolumn=\count335
\pgf@matrix@numberofcolumns=\count336
)
\tikz@expandcount=\count337
 (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))) (/usr/local/texlive/2022/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarycalc.code.tex
File: tikzlibrarycalc.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)

Package fontspec Info: Font family 'Lato-Lig(0)' created for font 'Lato-Lig'
(fontspec)             with options
(fontspec)             [Mapping=tex-text,Ligatures=TeX,Color=primary,Path =
(fontspec)             fonts/lato/].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[fonts/lato/Lato-Lig]/OT:script=latn;language=dflt;mapping=tex-text;color=2B2B2BFF
(fontspec)             ;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 


Package fontspec Info: Raleway-ExtraLight scale = 0.9567296970550606.


Package fontspec Info: Raleway-ExtraLight scale = 0.9567296970550606.


Package fontspec Info: Raleway-ExtraLight scale = 0.9567296970550606.


Package fontspec Info: Font family 'Raleway-ExtraLight(0)' created for font
(fontspec)             'Raleway-ExtraLight' with options
(fontspec)             [Mapping=tex-text,Ligatures=TeX,Scale=MatchLowercase,Mapping=tex-text,Path
(fontspec)             = fonts/raleway/].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[0.9567296970550606]"[fonts/raleway/Raleway-ExtraLight]/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->s*[0.9567296970550606]"[fonts/raleway/Raleway-ExtraLight]/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"


TextBlockOrigin set to 0mm x 5mm
) (/usr/local/texlive/2022/texmf-dist/tex/xelatex/xecjk/xunicode-addon.sty
Package: xunicode-addon 2021/12/12 v3.8.8 addon file for xunicode
\l__xunadd_tmp_coffin=\box72
\l__xunadd_circle_coffin=\box73
\l_xunadd_slot_int=\count338
) (/usr/local/texlive/2022/texmf-dist/tex/xelatex/xunicode/xunicode.sty
File: xunicode.sty 2011/09/09 v0.981 provides access to latin accents and many other characters in Unicode lower plane

*** Reloading Xunicode for encoding 'TU' ***
) (/usr/local/texlive/2022/texmf-dist/tex/xelatex/xecjk/xunicode-extra.def
File: xunicode-extra.def 2021/12/12 v3.8.8 extra definition for xunicode
) (./deedy_resume-openfont.aux)
\openout1 = `deedy_resume-openfont.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 49.
LaTeX Font Info:    ... okay on input line 49.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 49.
LaTeX Font Info:    ... okay on input line 49.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 49.
LaTeX Font Info:    ... okay on input line 49.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 49.
LaTeX Font Info:    ... okay on input line 49.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 49.
LaTeX Font Info:    ... okay on input line 49.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 49.
LaTeX Font Info:    ... okay on input line 49.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 49.
LaTeX Font Info:    ... okay on input line 49.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 49.
LaTeX Font Info:    ... okay on input line 49.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 49.
LaTeX Font Info:    ... okay on input line 49.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 49.
LaTeX Font Info:    ... okay on input line 49.
LaTeX Font Info:    Checking defaults for T3/cmr/m/n on input line 49.
LaTeX Font Info:    Trying to load font information for T3+cmr on input line 49.
 (/usr/local/texlive/2022/texmf-dist/tex/latex/tipa/t3cmr.fd
File: t3cmr.fd 2001/12/31 TIPA font definitions
)
LaTeX Font Info:    ... okay on input line 49.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(35.56593pt, 543.16313pt, 35.56593pt)
* v-part:(T,H,B)=(19.91684pt, 755.1363pt, 19.91684pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=543.16313pt
* \textheight=755.1363pt
* \oddsidemargin=-36.70406pt
* \evensidemargin=-36.70406pt
* \topmargin=-89.35315pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 49.
(/usr/local/texlive/2022/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section
 (/usr/local/texlive/2022/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/local/texlive/2022/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count339
)
LaTeX Info: Redefining \ref on input line 49.
LaTeX Info: Redefining \pageref on input line 49.
LaTeX Info: Redefining \nameref on input line 49.
 (./deedy_resume-openfont.out) (./deedy_resume-openfont.out)
\@outlinefile=\write4
\openout4 = `deedy_resume-openfont.out'.


isodate: babel.sty has been loaded

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 49.
LaTeX Font Info:    Redeclaring math accent \acute on input line 49.
LaTeX Font Info:    Redeclaring math accent \grave on input line 49.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 49.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 49.
LaTeX Font Info:    Redeclaring math accent \bar on input line 49.
LaTeX Font Info:    Redeclaring math accent \breve on input line 49.
LaTeX Font Info:    Redeclaring math accent \check on input line 49.
LaTeX Font Info:    Redeclaring math accent \hat on input line 49.
LaTeX Font Info:    Redeclaring math accent \dot on input line 49.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 49.
LaTeX Font Info:    Redeclaring math symbol \colon on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 49.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 49.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 49.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 49.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 49.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/Lato-Lig(0)/m/n on input line 49.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 49.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/Lato-Lig(0)/m/n on input line 49.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/Lato-Lig(0)/m/n --> TU/Lato-Lig(0)/m/n on input line 49.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/Lato-Lig(0)/m/it on input line 49.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/Lato-Lig(0)/b/n on input line 49.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/Raleway-ExtraLight(0)/m/n on input line 49.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 49.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/Lato-Lig(0)/m/n --> TU/Lato-Lig(0)/b/n on input line 49.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/Lato-Lig(0)/b/it on input line 49.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/Raleway-ExtraLight(0)/b/n on input line 49.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 49.

Package fontspec Warning: Font "FandolHei-Regular" does not contain requested
(fontspec)                Script "CJK".

LaTeX Font Info:    Font shape `TU/Raleway-ExtraLight(0)/m/n' will be
(Font)              scaled to size 9.56726pt on input line 59.

Package fontspec Info: Font family 'FandolHei-Regular(0)' created for font
(fontspec)             'FandolHei-Regular' with options
(fontspec)             [Mapping=tex-text,Script={CJK},Extension={.otf},BoldFont={FandolHei-Bold}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[FandolHei-Regular.otf]/OT:language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"[FandolHei-Bold.otf]/OT:language=dflt;mapping=tex-text;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 


Package fontspec Info: Font family 'Lato-Hai(0)' created for font 'Lato-Hai'
(fontspec)             with options [Mapping=tex-text,Path = fonts/lato/].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[fonts/lato/Lato-Hai]/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 


Package fontspec Info: Font family 'Lato-Lig(1)' created for font 'Lato-Lig'
(fontspec)             with options [Mapping=tex-text,Path = fonts/lato/].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[fonts/lato/Lato-Lig]/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 


Package fontspec Info: Font family 'Raleway-Medium(0)' created for font
(fontspec)             'Raleway-Medium' with options [Mapping=tex-text,Path =
(fontspec)             fonts/raleway/].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[fonts/raleway/Raleway-Medium]/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.:
(fontspec)             <->"[fonts/raleway/Raleway-Medium]/OT:script=latn;language=dflt;+smcp;mapping=tex-text;"

LaTeX Font Info:    Calculating math sizes for size <11> on input line 59.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <11> on input line 59.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7.69997> on input line 59.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <5.5> on input line 59.
File: avatar.jpg Graphic file (type bmp)
<avatar.jpg>

Underfull \hbox (badness 10000) in paragraph at lines 78--88

 []


Underfull \hbox (badness 10000) in paragraph at lines 78--88

 []


Underfull \hbox (badness 10000) in paragraph at lines 78--88

 []


Underfull \hbox (badness 10000) in paragraph at lines 78--88

 []


Underfull \hbox (badness 10000) in paragraph at lines 78--88

 []


Underfull \hbox (badness 10000) in paragraph at lines 78--88

 []


Underfull \hbox (badness 10000) in paragraph at lines 78--88

 []


Underfull \hbox (badness 10000) in paragraph at lines 78--88

 []


Underfull \hbox (badness 10000) in paragraph at lines 78--88

 []


Underfull \hbox (badness 10000) in paragraph at lines 78--88

 []


LaTeX Font Warning: Font shape `TU/Lato-Lig(0)/m/sc' undefined
(Font)              using `TU/Lato-Lig(0)/m/n' instead on input line 89.


LaTeX Font Warning: Font shape `TU/Lato-Lig(1)/m/sc' undefined
(Font)              using `TU/Lato-Lig(1)/m/n' instead on input line 89.


LaTeX Font Warning: Font shape `TU/FandolSong-Regular(0)/m/sc' undefined
(Font)              using `TU/FandolSong-Regular(0)/m/n' instead on input line 89.


Package fontspec Info: Font family 'Lato-Bol(0)' created for font 'Lato-Bol'
(fontspec)             with options [Mapping=tex-text,Path = fonts/lato/].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[fonts/lato/Lato-Bol]/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 


LaTeX Font Warning: Font shape `TU/Lato-Bol(0)/b/n' undefined
(Font)              using `TU/Lato-Bol(0)/m/n' instead on input line 91.


Package fontspec Info: Font family 'Lato-Reg(0)' created for font 'Lato-Reg'
(fontspec)             with options [Mapping=tex-text,Path = fonts/lato/].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[fonts/lato/Lato-Reg]/OT:script=latn;language=dflt;mapping=tex-text;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 

File: 实战打击.png Graphic file (type bmp)
<实战打击.png>

Package hyperref Warning: Token not allowed in a PDF string (Unicode):
(hyperref)                \hskip 11.0pt
(hyperref)                replaced by space on input line 112.


Package hyperref Warning: Suppressing link with empty target on input line 182.


Package hyperref Warning: Suppressing link with empty target on input line 191.


Package hyperref Warning: Suppressing link with empty target on input line 197.


Package hyperref Warning: Suppressing link with empty target on input line 203.

[1

] (./deedy_resume-openfont.aux)

LaTeX Font Warning: Some font shapes were not available, defaults substituted.

Package rerunfilecheck Info: File `deedy_resume-openfont.out' has not changed.
(rerunfilecheck)             Checksum: 656716B661C517CDBA2FADFF047FFD13;668.
 ) 
Here is how much of TeX's memory you used:
 31308 strings out of 476179
 651179 string characters out of 5809544
 983261 words of memory out of 5000000
 51534 multiletter control sequences out of 15000+600000
 473992 words of font info for 109 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 103i,6n,136p,429b,608s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on deedy_resume-openfont.pdf (1 page).
